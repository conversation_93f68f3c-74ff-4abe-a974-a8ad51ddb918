'use client';

import { useState, useEffect } from 'react';
import { Toaster } from '@/components/ui/sonner';
import { toast } from 'sonner';
import ImageUploader from '@/components/ImageUploader';
import ConversionSettings from '@/components/ConversionSettings';
import ConversionResult from '@/components/ConversionResult';
import imageCompression from 'browser-image-compression';
import { ChevronUp, ImageDown, ImageIcon, AlertTriangle, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { convertHeicToJpeg, isHeicFile } from '@/services/heicConverter';
import { detectBrowserCompatibility, getRecommendedOutputFormat, getBrowserWarnings, BrowserCompatibility } from '@/services/browserCompatibility';

interface ImageResult {
  originalSize: number;
  newSize: number;
  width: number;
  height: number;
  format: string;
  base64Image: string;
  fileName: string;
}

export default function Home() {
  const [images, setImages] = useState<File[]>([]);
  const [results, setResults] = useState<ImageResult[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [format, setFormat] = useState<string>('webp');
  const [quality, setQuality] = useState<number>(75);
  const [resizePercentage, setResizePercentage] = useState<number>(100); // 100% = tamanho original
  const [showScrollButton, setShowScrollButton] = useState<boolean>(false);
  const [browserCompatibility, setBrowserCompatibility] = useState<BrowserCompatibility | null>(null);
  const [browserWarnings, setBrowserWarnings] = useState<string[]>([]);

  // Efeito para detectar a compatibilidade do navegador
  useEffect(() => {
    // Detectar compatibilidade apenas no cliente
    if (typeof window !== 'undefined') {
      const compatibility = detectBrowserCompatibility();
      setBrowserCompatibility(compatibility);

      // Obter avisos baseados na compatibilidade
      const warnings = getBrowserWarnings(compatibility);
      setBrowserWarnings(warnings);

      // Mostrar avisos ao usuário se houver algum
      if (warnings.length > 0) {
        warnings.forEach(warning => {
          toast.warning(warning, { duration: 8000 });
        });
      }

      // Ajustar formato padrão com base na compatibilidade
      const recommendedFormat = getRecommendedOutputFormat(compatibility, 'webp');
      if (recommendedFormat !== 'webp') {
        setFormat(recommendedFormat);
        toast.info(`Formato de saída ajustado para ${recommendedFormat.toUpperCase()} para melhor compatibilidade com seu navegador.`, { duration: 5000 });
      }
    }
  }, []);

  // Efeito para detectar quando mostrar o botão de rolagem
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollButton(true);
      } else {
        setShowScrollButton(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleImageSelect = (files: File[]) => {
    // Verificar se há arquivos HEIC
    const heicFiles = files.filter(file => isHeicFile(file));

    if (heicFiles.length > 0) {
      toast.info(
        `${heicFiles.length} ${heicFiles.length === 1 ? 'arquivo HEIC detectado' : 'arquivos HEIC detectados'}. Estes serão convertidos para JPEG durante o processamento.`,
        { duration: 5000 }
      );
    }

    setImages(files);
    setResults([]);
  };

  const handleFormatChange = (value: string) => {
    setFormat(value);
  };

  const handleQualityChange = (value: number) => {
    setQuality(value);
  };

  const handleResizeChange = (value: number) => {
    setResizePercentage(value);
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const processImage = async (file: File): Promise<ImageResult> => {
    try {
      let fileToProcess = file;

      // Verificar se o arquivo é HEIC
      if (isHeicFile(file)) {
        try {
          // Converter HEIC para JPEG
          fileToProcess = await convertHeicToJpeg(file);
          toast.success(`Imagem HEIC "${file.name}" convertida para JPEG com sucesso!`);
        } catch (heicError) {
          console.error('Erro ao converter HEIC:', heicError);
          toast.error(`Falha ao converter imagem HEIC "${file.name}". Formato não suportado.`);
          throw new Error('Falha ao converter HEIC');
        }
      }

      // Determinar o tamanho alvo baseado no tamanho original e nas configurações
      const originalSizeMB = file.size / (1024 * 1024);
      let targetSizeMB;

      // Se a qualidade for alta (acima de 70%), ser menos agressivo na compressão
      if (quality > 70) {
        targetSizeMB = resizePercentage < 100 ? 1.0 : Math.max(1.0, originalSizeMB * 0.7); // Manter mais qualidade
      } else if (quality > 40) {
        targetSizeMB = resizePercentage < 100 ? 0.7 : Math.max(0.7, originalSizeMB * 0.5); // Compressão moderada
      } else {
        // Compressão mais agressiva para qualidade baixa
        targetSizeMB = resizePercentage < 100 ? 0.4 : Math.max(0.4, originalSizeMB * 0.3);
      }

      // Calcular o tamanho máximo com base na percentagem de redimensionamento
      // Se for 100%, não aplicamos redimensionamento
      const calculateMaxSize = () => {
        if (resizePercentage >= 100) return undefined;

        // Valor base de 1920 pixels para 100%
        // Ajustamos proporcionalmente com base na percentagem
        // Garantimos que o tamanho mínimo seja de 300px para evitar imagens muito pequenas
        const calculatedSize = Math.round(1920 * (resizePercentage / 100));
        return Math.max(calculatedSize, 300);
      };

      const maxSize = calculateMaxSize();

      // Adaptar configurações com base na compatibilidade do navegador
      let adaptedFormat = format;
      const adaptedQuality = quality / 100;

      // Se temos informações de compatibilidade, ajustar configurações
      if (browserCompatibility) {
        // Ajustar formato se necessário
        adaptedFormat = getRecommendedOutputFormat(browserCompatibility, format);

        // Em navegadores com limitações, podemos ajustar outros parâmetros
        if (browserCompatibility.isSafari || browserCompatibility.isIOS) {
          // Safari e iOS podem ter problemas com imagens muito grandes
          // Garantir que o tamanho máximo seja limitado
          if (originalSizeMB > 5 && resizePercentage >= 100) {
            // Sugerir ao usuário reduzir o tamanho para arquivos muito grandes
            toast.info(`Imagem grande detectada (${originalSizeMB.toFixed(1)}MB). Considere reduzir a escala para melhor desempenho.`, { duration: 5000 });
          }
        }
      }

      // Configurações de compressão
      const options = {
        maxSizeMB: targetSizeMB,
        ...(maxSize ? { maxWidthOrHeight: maxSize } : {}),
        useWebWorker: true,
        fileType: `image/${adaptedFormat}`,
        quality: adaptedQuality,
        initialQuality: adaptedQuality, // Qualidade inicial para JPEG e WEBP
        alwaysKeepResolution: resizePercentage >= 100, // Manter resolução original se 100%
      };

      // Comprimir a imagem
      const compressedFile = await imageCompression(fileToProcess, options);

      // Converter para base64
      const reader = new FileReader();

      return new Promise((resolve, reject) => {
        reader.onload = (event) => {
          if (!event.target) {
            reject(new Error('Falha ao ler o arquivo'));
            return;
          }

          const img = new Image();
          img.onload = () => {
            resolve({
              originalSize: file.size,
              newSize: compressedFile.size,
              width: img.width,
              height: img.height,
              format: format,
              base64Image: event.target?.result as string,
              fileName: file.name.replace(/\.[^/.]+$/, '') + '.' + format
            });
          };

          img.onerror = () => {
            reject(new Error('Falha ao carregar a imagem'));
          };

          img.src = event.target.result as string;
        };

        reader.onerror = () => {
          reject(new Error('Falha ao ler o arquivo'));
        };

        reader.readAsDataURL(compressedFile);
      });
    } catch (error) {
      console.error('Erro ao processar a imagem:', error);
      throw error;
    }
  };

  const handleConvert = async () => {
    if (images.length === 0) {
      toast.error('Selecione pelo menos uma imagem para converter');
      return;
    }

    setIsConverting(true);
    setResults([]);

    try {
      const processedResults: ImageResult[] = [];
      let heicErrorOccurred = false;

      for (const file of images) {
        try {
          const result = await processImage(file);
          processedResults.push(result);
        } catch (error) {
          console.error(`Erro ao processar ${file.name}:`, error);

          // Verificar se é um erro específico de HEIC
          if (error instanceof Error && error.message.includes('HEIC')) {
            heicErrorOccurred = true;
          }

          toast.error(`Falha ao processar ${file.name}`);
        }
      }

      setResults(processedResults);

      if (processedResults.length > 0) {
        toast.success(`${processedResults.length} ${processedResults.length === 1 ? 'imagem convertida' : 'imagens convertidas'} com sucesso!`);
      }

      // Mostrar mensagem específica para erros de HEIC
      if (heicErrorOccurred) {
        toast.error('Algumas imagens HEIC não puderam ser convertidas. Verifique se o seu navegador suporta este formato ou tente converter para JPEG antes de fazer upload.', {
          duration: 6000
        });
      }
    } catch (error) {
      console.error('Erro durante a conversão:', error);
      toast.error('Ocorreu um erro durante a conversão');
    } finally {
      setIsConverting(false);
    }
  };

  return (
    <div className="w-full min-h-screen">
      <Toaster position="top-right" />

      <div className="py-8 pb-10 min-h-[calc(100vh-2rem)]">
        {/* Avisos de compatibilidade do navegador */}
        {browserWarnings.length > 0 && (
          <div className="mb-6 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
              <div>
                <h3 className="text-sm font-medium text-amber-800 dark:text-amber-300">Compatibilidade do navegador</h3>
                <ul className="mt-2 text-xs text-amber-700 dark:text-amber-400 space-y-1">
                  {browserWarnings.map((warning, index) => (
                    <li key={index} className="flex items-start gap-1">
                      <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      <span>{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-10 min-h-[calc(100vh-10rem)]">
          {/* Área de upload - Ocupa 5 colunas em telas grandes */}
          <div className="lg:col-span-5 space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <ImageUploader
                onImageSelect={handleImageSelect}
                selectedFiles={images}
              />
            </div>

            {images.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <ConversionSettings
                  format={format}
                  quality={quality}
                  resizePercentage={resizePercentage}
                  onFormatChange={handleFormatChange}
                  onQualityChange={handleQualityChange}
                  onResizeChange={handleResizeChange}
                  onConvert={handleConvert}
                  isConverting={isConverting}
                  hasImages={images.length > 0}
                  imageCount={images.length}
                  browserCompatibility={browserCompatibility}
                />
              </div>
            )}
          </div>

          {/* Área de resultados - Ocupa 7 colunas em telas grandes */}
          <div className="lg:col-span-7 flex flex-col">
            {results.length > 0 ? (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden flex-1">
                <ConversionResult results={results} />
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm flex flex-col items-center justify-center text-center p-10 h-full min-h-[400px]">
                <div className="w-16 h-16 mb-6 rounded-full bg-blue-50 dark:bg-blue-900/20 flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                </div>
                <h3 className="heading-3 text-gray-900 dark:text-white mb-3">
                  A aguardar imagens para otimizar
                </h3>
                <p className="body-small max-w-md mb-8">
                  O PixelHunter irá reduzir o tamanho das suas imagens mantendo a qualidade visual. Ideal para websites, redes sociais e e-commerce.
                </p>

                {images.length > 0 && (
                  <Button
                    onClick={handleConvert}
                    disabled={isConverting}
                    className="flex items-center gap-2 px-8"
                    size="lg"
                  >
                    {isConverting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        A processar...
                      </>
                    ) : (
                      <>
                        <ImageDown className="h-5 w-5" />
                        Otimizar {images.length} {images.length === 1 ? 'imagem' : 'imagens'}
                      </>
                    )}
                  </Button>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {showScrollButton && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-300 z-50"
          aria-label="Voltar ao topo"
        >
          <ChevronUp className="h-5 w-5" />
        </button>
      )}
    </div>
  );
}
